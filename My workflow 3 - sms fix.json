{"name": "My workflow 3", "nodes": [{"parameters": {"rule": {"interval": [{"triggerAtHour": 6}]}}, "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [-16, -80], "id": "c23eb81b-3a5f-4d31-90eb-4ebdc5a260b3", "name": "Schedule Trigger"}, {"parameters": {"promptType": "define", "text": "# System Prompt (n8n + <PERSON><PERSON><PERSON>): Text Me on Calendar & VIP Gmail Events\n\n> **Environment:** n8n workflow with access to **Google Calendar**, **Gmail**, and **Twilio**.  \n> **Goal:** Send me a concise SMS via <PERSON>wi<PERSON> when (1) a calendar item is added, (2) a VIP sender emails me, or (3) it's 15 minutes before a scheduled meeting.  \n> **Timezone:** America/New_York.\n\n---\n\n## Mission\nYou are an automation agent operating **inside n8n**. Your job is to **produce exactly one Twilio SMS action** for each qualifying trigger below, with strong de-duplication and clear, actionable content.\n\n**Triggers**\n1) **Calendar Item Added** on my Google *Work* calendar  \n2) **VIP Email Received** in Gmail (allowed senders list below)  \n3) **Meeting Reminder at T-15:00** before any scheduled meeting on my Work calendar\n\n**VIP Senders (names → map to canonical emails in workflow env or Set node)**\n- <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>\n\n---\n\n## Available Tools\nYou have access to:\n1. **Google Calendar** - Can create events and get calendar information\n2. **Twilio SMS** - Can send SMS messages\n\n## Current Data\nThe workflow provides configuration data including:\n- TWILIO_FROM: {{ $json.TWILIO_FROM }}\n- SMS_TO: {{ $json.SMS_TO }}  \n- VIP_EMAILS: {{ $json.VIP_EMAILS }}\n- sessionId: {{ $json.sessionId }}\n\n## Your Task\nBased on the schedule trigger at 6 AM, you should:\n1. Check for any new calendar events that need notifications\n2. Check for VIP emails that need notifications\n3. Set up meeting reminders for events happening today\n4. Send appropriate SMS messages using the templates below\n\n## SMS Templates\n\n**Calendar Item Added:**\n[CAL NEW] {event_title} – {start_time}–{end_time} ET. Org: {organizer}. Att: {attendee_count}. {location_or_link}. ID:{event_id_last4}\n\n**VIP Email Received:**\n[VIP EMAIL] {from_name}: \"{subject_trimmed}\". Recvd {received_time} ET. \"{snippet_trimmed}\"\n\n**15-Minute Meeting Reminder:**\n[MTG T-15] {event_title} @ {start_time} ET. {location_or_link}. Org: {organizer}. Att: {attendee_count}. ID:{event_id_last4}\n\n## Instructions\n1. Use the Google Calendar tool to check for today's events\n2. For any events starting within the next 24 hours, set up appropriate notifications\n3. Use the Twilio tool to send SMS messages using FROM: {{ $json.TWILIO_FROM }} and TO: {{ $json.SMS_TO }}\n4. Keep messages under 240 characters when possible\n5. Use Eastern Time (America/New_York) for all time displays\n6. Only process emails from VIP senders: {{ $json.VIP_EMAILS }}", "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2.2, "position": [400, -80], "id": "b37e227d-5ba5-412e-b65e-20bdb1ff965e", "name": "AI Agent"}, {"parameters": {"model": {"__rl": true, "value": "gpt-4o-mini", "mode": "list", "cachedResultName": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-144, 224], "id": "726e9f7c-54a6-4102-b1c3-e33a35acbbe5", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "SBYGN5J93DAikKnC", "name": "OpenAi account"}}}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "test-session"}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [224, 224], "id": "7378314a-ed1f-4d3a-bfce-5e6292780138", "name": "Simple Memory"}, {"parameters": {"assignments": {"assignments": [{"id": "da4bd308-36f9-435a-8ed5-ba907670b2bb", "name": "TWILIO_FROM", "value": "+**********", "type": "string"}, {"id": "ea20ceef-d34f-40f1-bbf7-f2d48f9b6872", "name": "SMS_TO", "value": "+**********", "type": "string"}, {"id": "16dc1acd-02a6-465e-b3da-9be94a0300c8", "name": "VIP_EMAILS", "value": "<EMAIL>", "type": "string"}, {"id": "346ca3a4-4bda-4d01-a585-cdd62c25262a", "name": "sessionId", "value": "={{ $workflow.id }}-{{ $runIndex }}", "type": "string"}, {"id": "suppress-private", "name": "SUPPRESS_PRIVATE_EVENTS", "value": true, "type": "boolean"}, {"id": "quiet-hours", "name": "QUIET_HOURS", "value": "22:00–07:00", "type": "string"}, {"id": "update-threshold", "name": "UPDATE_THRESHOLD_MIN", "value": 10, "type": "number"}, {"id": "short-notice", "name": "SHORT_NOTICE_WINDOW_MIN", "value": 15, "type": "number"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [192, -80], "id": "f9b9d5ad-52e9-4f6d-98a6-96371f381e43", "name": "Configuration Variables"}, {"parameters": {"calendar": {"__rl": true, "value": "<EMAIL>", "mode": "list", "cachedResultName": "<EMAIL>"}, "additionalFields": {}}, "type": "n8n-nodes-base.googleCalendarTool", "typeVersion": 1.3, "position": [384, 208], "id": "58d9e76b-d665-4ba0-86c8-eca1f67b29df", "name": "Create an event in Google Calendar", "credentials": {"googleCalendarOAuth2Api": {"id": "injFyzFpjUbd3ZWN", "name": "Google Calendar account"}}}, {"parameters": {"from": "={{$json.TWILIO_FROM}}", "to": "={{$json.SMS_TO}}", "message": "Work message", "options": {"statusCallback": ""}}, "type": "n8n-nodes-base.twilioTool", "typeVersion": 1, "position": [544, 208], "id": "3adbd8d5-e93d-4e97-90a7-341d3cb4fafa", "name": "Send an SMS/MMS/WhatsApp message in Twilio", "credentials": {"twilioApi": {"id": "7VuCarwwBq6rg97p", "name": "Twilio account"}}}], "pinData": {}, "connections": {"Schedule Trigger": {"main": [[{"node": "Configuration Variables", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Send an SMS/MMS/WhatsApp message in Twilio": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Create an event in Google Calendar": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Configuration Variables": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "72ebd0da-55cb-4ab6-81d5-ced9c67b7684", "meta": {"templateCredsSetupCompleted": true, "instanceId": "cf7eb7065260a44eb05fc67c96af94d7f15579fd016dd0de40ad0831671a47d5"}, "id": "hNoucPLPGKlA6HX0", "tags": []}